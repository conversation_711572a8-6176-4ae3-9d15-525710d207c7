<?php
session_start();
require '../db.php'; // Your database connection, assume PDO

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_SESSION['user_id'];
    $name = trim($_POST['name']);
    $amount = floatval($_POST['amount']);

    // Use the datetime from form, or current time if not provided
    $date = isset($_POST['datetime']) && !empty($_POST['datetime'])
        ? $_POST['datetime']
        : date('Y-m-d H:i:s');

    // Basic validation
    if ($name != '' && $amount > 0) {
        $stmt = $pdo->prepare("INSERT INTO expenses (user_id, name, amount, date) VALUES (?, ?, ?, ?)");
        $stmt->execute([$user_id, $name, $amount, $date]);
        header('Location: addexpense.php'); // back to add expense page
        exit();
    } else {
        // handle validation error if needed
        die('Invalid input.');
    }
}
?>