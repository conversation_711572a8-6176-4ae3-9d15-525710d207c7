/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', sans-serif;
    background: white;  
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position:relative;
    top:0;
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: fixed;
    top:0;
    z-index: 1000;
}

main.container {
    flex: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color:whitesmoke;
    border-radius: 10px;
    margin: 80px auto;
    width: 800px;
    height: auto;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
    content: normal; 
    position: relative;
    color:black;
    top: 150px;

}

/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    position: fixed;
    top: 90px;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.4); */
    width: 100%;
    z-index: 1000;
}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    /* transition: background 0.3s ease; */
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Sidebar */
aside {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 20px;
    width: 250px;
    height: 300vh; /* Account for header + nav height */
    height: 100%;
    position: fixed;
    top: 150px;
    /* border-right: 2px solid rgba(255, 255, 255, 0.1); */
    overflow-y: auto;
}

aside h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #f5f5f5;
}

aside ul {
    list-style-type: none;
}

aside ul li {
    margin-bottom: 15px;
}

aside ul li a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

aside ul li a:hover {
    color: #00adb5;
}

/* For responsive layout */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 10px;
    }

    aside {
        width: 100%;
        position: relative;
        min-height: auto;
        border-right: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 20px;
    }
}
.btn {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 1rem;
}
.btn:hover {
    background-color: rgba(0, 0, 0, 0.25);
}
.btn-cancel {
    background-color: #3498db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    color: white;
    border-radius: 5px;
    display: inline-block;
    margin-top: 1rem;
}
.btn-cancel:hover {
    background-color: #2980b9;
}
.expense-list {
    margin-top: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
    width: 700px;
    height: auto;
}
.expense-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    color: #333;
}
.expense-item:last-child {
    border-bottom: none;
}
.btn-delete, .btn-edit {
    color: white;
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
}
.btn-delete {
    background-color: #e74c3c;
}
.btn-edit {
    background-color: #3498db;
}
.btn-delete:hover {
    background-color: #c0392b;
}
.btn-edit:hover {
    background-color: #2980b9;
}
canvas {
    margin-top: 2rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
/* Footer */
footer {
    text-align: center;
    padding: 1rem;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    margin-top: 2rem;
    width: 100%;
    position: absolute;
}
.error {
    color: red;
    margin-bottom: 1rem;
}